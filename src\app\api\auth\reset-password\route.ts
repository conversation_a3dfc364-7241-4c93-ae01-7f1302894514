import { forgotPasswordAction } from '@/services/auth/actions/index';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Converter JSON para FormData para manter compatibilidade
    const formData = new FormData();
    formData.append('email', body.email || '');

    if (body.hcaptchaToken) {
      formData.append('hcaptchaToken', body.hcaptchaToken);
    }

    if (body.redirect_url) {
      formData.append('redirect_url', body.redirect_url);
    }

    const result = await forgotPasswordAction(formData);

    if ('redirect' in result) {
      return NextResponse.json({
        success: true,
        redirect: result.redirect
      });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Erro ao processar redefinição de senha:', error);
    return NextResponse.json(
      {
        error: 'Falha ao processar a solicitação de redefinição de senha',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}